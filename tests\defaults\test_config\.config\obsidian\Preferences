{"browser": {"enable_spellchecking": true}, "electron": {"devtools": {"preferences": {"Inspector.drawerSplitViewState": "{\"horizontal\":{\"size\":0,\"showMode\":\"OnlyMain\"}}", "InspectorView.splitViewState": "{\"vertical\":{\"size\":0},\"horizontal\":{\"size\":353}}", "Styles-pane-sidebar-tabOrder": "{\"Styles\":10,\"Computed\":20}", "adornerSettings": "[{\"adorner\":\"grid\",\"isEnabled\":true},{\"adorner\":\"flex\",\"isEnabled\":true},{\"adorner\":\"ad\",\"isEnabled\":true},{\"adorner\":\"scroll-snap\",\"isEnabled\":true},{\"adorner\":\"container\",\"isEnabled\":true}]", "closeableTabs": "{\"security\":true}", "console.sidebar.width": "{\"vertical\":{\"size\":0,\"showMode\":\"OnlyMain\"}}", "console.sidebarSelectedFilter": "\"message\"", "currentDockState": "\"bottom\"", "elements.styles.sidebar.width": "{\"vertical\":{\"size\":0,\"showMode\":\"OnlyMain\"}}", "elementsPanelSplitViewState": "{\"horizontal\":{\"size\":145}}", "inspectorVersion": "31", "lastDockState": "\"right\"", "panel-selectedTab": "\"console\"", "sourcesPanelNavigatorSplitViewState": "{\"vertical\":{\"size\":0,\"showMode\":\"Both\"}}", "sourcesPanelSplitViewState": "{\"vertical\":{\"size\":216,\"showMode\":\"Both\"},\"horizontal\":{\"size\":68,\"showMode\":\"Both\"}}", "undefined-tabOrder": "{\"sources.scopeChain\":10,\"sources.watch\":20,\"Styles\":1,\"Computed\":2}"}}}, "partition": {"per_host_zoom_levels": {"2394503984146568780": {}}}, "spellcheck": {"dictionaries": ["en-US"], "dictionary": ""}}