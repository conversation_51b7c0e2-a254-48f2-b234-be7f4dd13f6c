name: Test e2e

env:
  PLUGIN_NAME: obsidian-to-anki-plugin
  NODE_V: '18.x'

on:
  # push:
  #   branches: [ master ]
  pull_request:
    branches: [ master ]
  repository_dispatch:
    types: [ok-to-test-command]

permissions:
  checks: write
  pull-requests: write

jobs:
  checkout-trusted:
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request' && github.event.pull_request.head.repo.full_name == github.repository
    steps:
      - name: Branch based PR checkout
        uses: actions/checkout@v4
      - name: Use Node.js ${{ env.NODE_V }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_V }}
      - run: |
          npm install obsidian 
          npm ci
      - run: npm run build --if-present
      
      - name: Run Tests
        run: | 
          # npm run test
          sudo npm run test-wdio
          sudo npm run test-py
      
      - name: Set output perms
        if: always()
        run: |
          echo "Current user for this action is - $USER $(id -u)"
          sudo chown -R $(whoami) tests/test_config
          sudo chown -R $(whoami) tests/test_vault

          ls -alh
          ls -alh tests/test_config/
          ls -alh "tests/test_config/.local/share/Anki2/User 1/"

      - name: Package
        if: always()
        run: |
          mkdir ${{ env.PLUGIN_NAME }}
          cp main.js manifest.json styles.css README.md ${{ env.PLUGIN_NAME }}
          zip -r ${{ env.PLUGIN_NAME }}.zip ${{ env.PLUGIN_NAME }}

      - name: Publish Test Results in Comment
        uses: EnricoMi/publish-unit-test-result-action@v2
        # if: github.event_name == 'pull_request' && github.event.pull_request.head.repo.full_name == github.repository && always()
        if: always()

        with:
          report_individual_runs: true
          report_suite_logs: 'any'
          deduplicate_classes_by_file_name: true          
          comment_title: '🛠 Test Results'
          files: |
            logs/test-reports/**/*.xml

      # - name: Publish Test Results
      #   uses: dorny/test-reporter@v1
      #   if: success() || failure()
      #   with:
      #     name: JUnit Reprot
      #     path: logs/test-reports/wdio.xml
      #     reporter: jest-junit      
      - uses: actions/setup-node@v4
        with:
          node-version: 16
      - uses: iterative/setup-cml@v1
        if: always()
      
      - name: Publish Screenshots - Find Comment
        if: always()
        uses: peter-evans/find-comment@v3
        id: fc
        with:
          issue-number: ${{ github.event.number }}
          body-includes: '# 📷 Screenshots of tests:'
          direction: last
          comment-author: 'github-actions[bot]'
      
      - name: Publish Screenshots - build comment
        if: always()
        env:
          REPO_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          EVENT: ${{ github.event_name }}
          REF: ${{ github.ref }}
        shell: bash
        run: |
          echo "# 📷 Screenshots of tests:" >> comment.md
          echo "" >> comment.md

          for ss_test in logs/**/; do 
            ss_test_desc=$(basename "$ss_test" .png)
            
            if [ "$ss_test_desc" == "test-reports" ]; then
              continue
            fi

            echo "" >> comment.md
            echo "### 🔧 $ss_test_desc" >> comment.md
            echo "$ss_test - $ss_test_desc"
            echo "" >> comment.md

            # echo '<details><summary>Pre Test</summary>' >> comment.md
            echo '#### Pre Test' >> comment.md

            for ss in "$ss_test"*Pre*.png; do 
              echo '<p float="left">' >> comment.md
              echo "  $ss"
              cml-publish "$ss" | sed -E 's/.+/<img width="90%" src="\0"\/>/' >> comment.md
              echo '</p>' >> comment.md
            done 
            
            echo "" >> comment.md
            # echo '</details><details><summary>Post Test</summary>' >> comment.md
            echo '#### Post Test' >> comment.md

            for ss in "$ss_test"*Post*.png; do 
              echo '<p float="left">' >> comment.md
              echo "  $ss"
              cml-publish "$ss" | sed -E 's/.+/<img width="90%" src="\0"\/>/' >> comment.md
              echo '</p>' >> comment.md
            done 

            # echo '</details>' >> comment.md
            echo "" >> comment.md
          done

          # for ss_test in tests/test_config/*.png logs/*.png; do 
          #   ss_test_desc=$(basename "$ss_test" .png)

          #   # echo "<details><summary>$ss_test_desc</summary>" >> comment.md
          #   # echo '<p float="left">' >> comment.md
          #   # cml-publish "$ss_test" | sed -E 's/.+/<img width="90%" src="\0"\/>/' >> comment.md
          #   # # echo "![](./$ss_test)" >> comment.md
          #   # echo '</p></details>' >> comment.md

          #   echo "" >> comment.md
          #   echo "### $ss_test_desc" >> comment.md
          #   echo "" >> comment.md
          #   echo '<p float="left">' >> comment.md
          #   cml-publish "$ss_test" | sed -E 's/.+/<img width="90%" src="\0"\/>/' >> comment.md
          #   # echo "![](./$ss_test)" >> comment.md
          #   echo '</p>' >> comment.md
          # done

          if [ "$EVENT" == 'pull_request' ]
          then
            sha=${{ github.event.pull_request.head.sha}}
          elif [ "$EVENT" == 'workflow_run' ]
          then
            sha=${{ github.event.workflow_run.head_sha}}
          else
            sha=$GITHUB_SHA
          fi

          echo "" >> comment.md
          echo "###### For commit $sha" >> comment.md
      
      - name: Publish Screehnshots - Update or Create Comment
        if: github.event_name == 'pull_request' && github.event.pull_request.head.repo.full_name == github.repository && always()
        uses: peter-evans/create-or-update-comment@v4
        with:
          comment-id: ${{ steps.fc.outputs.comment-id }}
          body-file: comment.md
          edit-mode: replace
          issue-number: ${{ github.event.number }}

      - name: Upload build artifacts
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: ${{ env.PLUGIN_NAME }}
          path: |
            ${{ env.PLUGIN_NAME }}.zip
            logs/*
            tests/test_config/logs/*
            tests/test_config/*.png
            # tests/test_config/.xsession-errors

      - name: Upload build artifacts for failed builds
        if: failure()
        uses: actions/upload-artifact@v3
        with:
          name: ${{ env.PLUGIN_NAME }}-fail
          path: |
            tests/test_config
            tests/test_vault

  checkout-signed:
    runs-on: ubuntu-latest
    permissions:
      pull-requests: write
      checks: write
    if: |
      github.event_name == 'repository_dispatch' &&
      github.event.client_payload.slash_command.args.named.sha != '' &&
      contains(
        github.event.client_payload.pull_request.head.sha,
        github.event.client_payload.slash_command.args.named.sha
      )
    steps:
      # Check out merge commit
      - name: Fork based /ok-to-test checkout
        uses: actions/checkout@v4
        with:
          ref: 'refs/pull/${{ github.event.client_payload.pull_request.number }}/merge'

  # Test:
  #   if: always()
  #   needs: [checkout-trusted, checkout-signed]
  #   runs-on: ubuntu-latest

  #   strategy:
  #     matrix:
  #       node-version: [18.x]

  # steps:
      # - name: Trusted Checkout
      #   if: github.event_name == 'pull_request' && github.event.pull_request.head.repo.full_name == github.repository
      #   uses: actions/checkout@v4
      - name: Use Node.js ${{ env.NODE_V }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_V }}
      - run: |
          npm install obsidian 
          npm ci
      - run: npm run build --if-present
      # - run: npm test
      
      - name: Run Tests
        run: | 
          # npm run test
          sudo npm run test-wdio
          sudo npm run test-py
      
      - name: Set output perms
        if: always()
        run: |
          echo "Current user for this action is - $USER $(id -u)"
          sudo chown -R $(whoami) tests/test_config
          sudo chown -R $(whoami) tests/test_vault

          ls -alh
          ls -alh tests/test_config/
          ls -alh "tests/test_config/.local/share/Anki2/User 1/"

      - name: Package
        if: always()
        run: |
          mkdir ${{ env.PLUGIN_NAME }}
          cp main.js manifest.json styles.css README.md ${{ env.PLUGIN_NAME }}
          zip -r ${{ env.PLUGIN_NAME }}.zip ${{ env.PLUGIN_NAME }}

      - name: Publish Test Results in Comment
        uses: EnricoMi/publish-unit-test-result-action@v2
        if: always()
        with:
          report_individual_runs: true
          report_suite_logs: 'any'
          deduplicate_classes_by_file_name: true
          comment_title: '🛠 Test Results'
          commit: ${{ github.event.client_payload.pull_request.head.sha }}
          files: |
            logs/test-reports/**/*.xml

      # - name: Publish Test Results
      #   uses: dorny/test-reporter@v1
      #   if: success() || failure()
      #   with:
      #     name: JUnit Reprot
      #     path: logs/test-reports/wdio.xml
      #     reporter: jest-junit
      
      - uses: iterative/setup-cml@v1
        if: always()
      
      - name: Publish Screenshots - Find Comment
        if: always()
        uses: peter-evans/find-comment@v3
        id: fc
        with:
          issue-number: ${{ github.event.client_payload.pull_request.number }}
          body-includes: '# 📷 Screenshots of tests:'
          direction: last
          comment-author: 'github-actions[bot]'
      
      - name: Publish Screenshots - build comment
        if: always()
        env:
          REPO_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          EVENT: ${{ github.event_name }}
          REF: ${{ github.ref }}
        shell: bash
        run: |
          echo "# 📷 Screenshots of tests:" >> comment.md
          echo "" >> comment.md

          for ss_test in logs/**/; do 
            ss_test_desc=$(basename "$ss_test" .png)
            
            if [ "$ss_test_desc" == "test-reports" ]; then
              continue
            fi

            echo "" >> comment.md
            echo "### 🔧 $ss_test_desc" >> comment.md
            echo "" >> comment.md

            echo '<details><summary>Pre Test</summary>' >> comment.md

            for ss in "$ss_test"*Pre*.png; do 
              echo '<p float="left">' >> comment.md
              cml-publish "$ss" | sed -E 's/.+/<img width="90%" src="\0"\/>/' >> comment.md
              echo '</p>' >> comment.md
            done 
            
            echo '</details><details><summary>Post Test</summary>' >> comment.md

            for ss in "$ss_test"*Post*.png; do 
              echo '<p float="left">' >> comment.md
              cml-publish "$ss" | sed -E 's/.+/<img width="90%" src="\0"\/>/' >> comment.md
              echo '</p>' >> comment.md
            done 

            echo '</details>' >> comment.md
          done

          # for ss_test in tests/test_config/*.png logs/*.png; do 
          #   ss_test_desc=$(basename "$ss_test" .png)

          #   # echo "<details><summary>$ss_test_desc</summary>" >> comment.md
          #   # echo '<p float="left">' >> comment.md
          #   # cml-publish "$ss_test" | sed -E 's/.+/<img width="90%" src="\0"\/>/' >> comment.md
          #   # # echo "![](./$ss_test)" >> comment.md
          #   # echo '</p></details>' >> comment.md

          #   echo "" >> comment.md
          #   echo "### $ss_test_desc" >> comment.md
          #   echo "" >> comment.md
          #   echo '<p float="left">' >> comment.md
          #   cml-publish "$ss_test" | sed -E 's/.+/<img width="90%" src="\0"\/>/' >> comment.md
          #   # echo "![](./$ss_test)" >> comment.md
          #   echo '</p>' >> comment.md
          # done

          # if [ "$EVENT" == 'pull_request' ]
          # then
          sha=${{ github.event.client_payload.pull_request.head.sha }}
          # elif [ "$EVENT" == 'workflow_run' ]
          # then
          #   sha=${{ github.event.workflow_run.head_sha}}
          # else
          #   sha=$GITHUB_SHA
          # fi

          echo "" >> comment.md
          echo "###### For commit $sha" >> comment.md
      
      - name: Publish Screehnshots - Update or Create Comment
        if: always()
        uses: peter-evans/create-or-update-comment@v4
        with:
          comment-id: ${{ steps.fc.outputs.comment-id }}
          body-file: comment.md
          edit-mode: replace
          issue-number: ${{ github.event.client_payload.pull_request.number }}

      - name: Upload build artifacts
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: ${{ env.PLUGIN_NAME }}
          path: |
            ${{ env.PLUGIN_NAME }}.zip
            logs/*
            tests/test_config/logs/*
            tests/test_config/*.png
            # tests/test_config/.xsession-errors

      - name: Upload build artifacts for failed builds
        if: failure()
        uses: actions/upload-artifact@v3
        with:
          name: ${{ env.PLUGIN_NAME }}-fail
          path: |
            tests/test_config
            tests/test_vault
    
  # MapjobStatus:
  #   needs: [Test]
  #   runs-on: ubuntu-latest
  #   if: |
  #     github.event_name == 'repository_dispatch' &&
  #     github.event.client_payload.slash_command.args.named.sha != '' &&
  #     contains(
  #       github.event.client_payload.pull_request.head.sha,
  #       github.event.client_payload.slash_command.args.named.sha
  #     )
    
  #   steps:
      - uses: actions/github-script@v7
        id: update-check-run
        if: ${{ always() }}
        env:
          number: ${{ github.event.client_payload.pull_request.number }}
          job: ${{ github.job }}
          # Conveniently, job.status maps to https://developer.github.com/v3/checks/runs/#update-a-check-run
          conclusion: ${{ job.status }} 
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const { data: pull } = await github.rest.pulls.get({
              ...context.repo,
              pull_number: process.env.number
            });
            const ref = pull.head.sha;
            const { data: checks } = await github.rest.checks.listForRef({
              ...context.repo,
              ref
            });
            const check = checks.check_runs.filter(c => c.name === process.env.job);
            if( check ) {
              const { data: result } = await github.rest.checks.update({
                ...context.repo,
                check_run_id: check[0].id,
                status: 'completed',
                conclusion: process.env.conclusion
              });
              return result;
            }

